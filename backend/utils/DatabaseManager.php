<?php
require_once __DIR__ . '/../config/database.php';

/**
 * Database Manager for Reviews
 */
class DatabaseManager {
    private $conn;
    private $table_name = "reviews";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Insert a new review
     */
    public function insertReview($app_name, $store_name, $country_name, $rating, $review_content, $review_date) {
        $query = "INSERT INTO " . $this->table_name . "
                  (app_name, store_name, country_name, rating, review_content, review_date)
                  VALUES (:app_name, :store_name, :country_name, :rating, :review_content, :review_date)";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $app_name = htmlspecialchars(strip_tags($app_name));
        $store_name = htmlspecialchars(strip_tags($store_name));
        $country_name = htmlspecialchars(strip_tags($country_name));
        $rating = intval($rating);
        $review_content = htmlspecialchars(strip_tags($review_content));

        // Bind values
        $stmt->bindParam(":app_name", $app_name);
        $stmt->bindParam(":store_name", $store_name);
        $stmt->bindParam(":country_name", $country_name);
        $stmt->bindParam(":rating", $rating);
        $stmt->bindParam(":review_content", $review_content);
        $stmt->bindParam(":review_date", $review_date);

        if($stmt->execute()) {
            return true;
        }
        return false;
    }

    /**
     * Check if review already exists (prevent duplicates)
     */
    public function reviewExists($app_name, $store_name, $review_content, $review_date) {
        $query = "SELECT id FROM " . $this->table_name . "
                  WHERE app_name = :app_name
                  AND store_name = :store_name
                  AND review_content = :review_content
                  AND review_date = :review_date
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":app_name", $app_name);
        $stmt->bindParam(":store_name", $store_name);
        $stmt->bindParam(":review_content", $review_content);
        $stmt->bindParam(":review_date", $review_date);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    /**
     * Get reviews count for current month
     */
    public function getThisMonthReviews($app_name = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . "
                  WHERE MONTH(review_date) = MONTH(CURDATE())
                  AND YEAR(review_date) = YEAR(CURDATE())";

        if ($app_name) {
            $query .= " AND app_name = :app_name";
        }

        $stmt = $this->conn->prepare($query);
        if ($app_name) {
            $stmt->bindParam(":app_name", $app_name);
        }
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['count'];
    }

    /**
     * Get reviews count for last 30 days
     */
    public function getLast30DaysReviews($app_name = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . "
                  WHERE review_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";

        if ($app_name) {
            $query .= " AND app_name = :app_name";
        }

        $stmt = $this->conn->prepare($query);
        if ($app_name) {
            $stmt->bindParam(":app_name", $app_name);
        }
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['count'];
    }

    /**
     * Get average rating
     */
    public function getAverageRating($app_name = null) {
        if ($app_name) {
            // Try to get data from metadata table first
            $metaQuery = "SELECT overall_rating FROM app_metadata WHERE app_name = :app_name";
            $stmt = $this->conn->prepare($metaQuery);
            $stmt->bindParam(":app_name", $app_name);
            $stmt->execute();
            $metaResult = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($metaResult) {
                return floatval($metaResult['overall_rating']);
            }
        }

        // Fallback to actual review data
        $query = "SELECT AVG(rating) as avg_rating FROM " . $this->table_name;

        if ($app_name) {
            $query .= " WHERE app_name = :app_name";
        }

        $stmt = $this->conn->prepare($query);
        if ($app_name) {
            $stmt->bindParam(":app_name", $app_name);
        }
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return round($result['avg_rating'], 1);
    }

    /**
     * Get review distribution by rating
     */
    public function getReviewDistribution($app_name = null) {
        if ($app_name) {
            // Try to get data from metadata table first
            $metaQuery = "SELECT
                            total_reviews,
                            five_star_total as five_star,
                            four_star_total as four_star,
                            three_star_total as three_star,
                            two_star_total as two_star,
                            one_star_total as one_star
                          FROM app_metadata
                          WHERE app_name = :app_name";

            $stmt = $this->conn->prepare($metaQuery);
            $stmt->bindParam(":app_name", $app_name);
            $stmt->execute();
            $metaResult = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($metaResult) {
                return $metaResult;
            }
        }

        // Fallback to actual review data
        $query = "SELECT
                    COUNT(*) as total_reviews,
                    SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
                    SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
                    SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
                    SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
                    SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
                  FROM " . $this->table_name;

        if ($app_name) {
            $query .= " WHERE app_name = :app_name";
        }

        $stmt = $this->conn->prepare($query);
        if ($app_name) {
            $stmt->bindParam(":app_name", $app_name);
        }
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result;
    }

    /**
     * Get latest 10 reviews
     */
    public function getLatestReviews($limit = 10, $app_name = null) {
        $query = "SELECT app_name, store_name, country_name, rating, review_content, review_date
                  FROM " . $this->table_name;

        if ($app_name) {
            $query .= " WHERE app_name = :app_name";
        }

        $query .= " ORDER BY review_date DESC, created_at DESC LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        if ($app_name) {
            $stmt->bindParam(":app_name", $app_name);
        }
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
