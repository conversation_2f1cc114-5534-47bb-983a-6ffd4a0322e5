.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  width: 100%;
  box-sizing: border-box;
}

.app-header {
  text-align: center;
  margin-bottom: 3rem;
  color: white;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 auto;
}

.app-main {
  width: 100%;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Summary Stats Styles */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card h3 {
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.stat-card .stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  color: #888;
  font-size: 0.9rem;
}

/* App Selector Styles */
.app-selector {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin: 0 auto 2rem auto;
  max-width: 500px;
  text-align: center;
}

.selector-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.selector-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.app-dropdown {
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
  width: 100%;
  max-width: 300px;
  text-align: center;
  font-weight: 500;
  color: #000;
}

.app-dropdown option {
  color: #000;
  background: white;
}

.app-dropdown:hover {
  border-color: #667eea;
}

.app-dropdown:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.app-dropdown:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.scraping-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 8px;
  color: #1976d2;
  font-weight: 500;
  max-width: 350px;
  margin: 0 auto;
  text-align: center;
}

.scraping-status > span {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.scraping-details {
  margin-top: 0.25rem;
}

.scraping-details small {
  color: #1565c0;
  font-size: 0.85rem;
  opacity: 0.8;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e3f2fd;
  border-top: 2px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.selected-app-display {
  margin-top: 1rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 2px solid #667eea;
  border-radius: 12px;
  text-align: center;
  max-width: 350px;
  margin-left: auto;
  margin-right: auto;
}

.selected-app-display h3 {
  margin: 0 0 0.25rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.selected-app-display p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.success-message {
  margin-top: 1rem;
  padding: 1rem;
  background: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 8px;
  color: #2e7d32;
  font-weight: 500;
}

.error-message {
  margin-top: 1rem;
  padding: 1rem;
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 8px;
  color: #c62828;
  font-weight: 500;
}

/* Review Distribution Styles */
.review-distribution {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.review-distribution h2 {
  color: #333;
  margin: 0;
  font-size: 1.5rem;
}

.total-reviews-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.total-count {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.total-label {
  font-size: 0.9rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.distribution-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.distribution-item {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.distribution-item:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.distribution-item .star-label {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: #ffc107;
}

.distribution-item .count {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

/* Latest Reviews Styles */
.latest-reviews {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.latest-reviews h2 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.review-item {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.review-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.review-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.review-content {
  color: #333;
  line-height: 1.6;
  margin-top: 0.5rem;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  text-align: center;
  padding: 2rem;
  color: #dc3545;
  background: #f8d7da;
  border-radius: 8px;
  margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 0.5rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .distribution-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .review-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .distribution-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .total-reviews-badge {
    align-self: stretch;
    text-align: center;
  }

  .app-selector {
    margin: 0 0 2rem 0;
    padding: 1.5rem;
  }

  .app-dropdown {
    max-width: 280px;
  }

  .selected-app-display {
    max-width: 300px;
    padding: 0.75rem 1rem;
  }

  .scraping-status {
    max-width: 300px;
    padding: 0.75rem;
  }
}
