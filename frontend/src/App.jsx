import { useState, useEffect } from 'react'
import './App.css'
import AppSelector from './components/AppSelector'
import SummaryStats from './components/SummaryStats'
import ReviewDistribution from './components/ReviewDistribution'
import LatestReviews from './components/LatestReviews'

function App() {
  const [selectedApp, setSelectedApp] = useState('StoreSEO'); // Default to StoreSEO
  const [refreshKey, setRefreshKey] = useState(0);

  const handleAppSelect = (appName) => {
    setSelectedApp(appName);
  };

  const handleScrapeComplete = (appName, scrapedCount) => {
    console.log(`Scraping completed for ${appName}: ${scrapedCount} new reviews`);
    // Trigger refresh of all components
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Shopify App Review Analytics</h1>
        <p>Comprehensive analytics dashboard for tracking and analyzing Shopify app reviews</p>
      </header>

      <main className="app-main">
        <AppSelector
          selectedApp={selectedApp}
          onAppSelect={handleAppSelect}
          onScrapeComplete={handleScrapeComplete}
        />

        <SummaryStats
          selectedApp={selectedApp}
          refreshKey={refreshKey}
        />
        <ReviewDistribution
          selectedApp={selectedApp}
          refreshKey={refreshKey}
        />
        <LatestReviews
          selectedApp={selectedApp}
          refreshKey={refreshKey}
        />
      </main>
    </div>
  )
}

export default App
